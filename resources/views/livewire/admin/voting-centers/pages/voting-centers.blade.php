<div>
    <flux:heading size="xl">{{ __('voting_centers.title') }}</flux:heading>
    <flux:subheading>{{ __('voting_centers.title_description') }}</flux:subheading>

    <div class="mt-6">
        <flux:card>
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">{{ __('voting_centers.listing') }}</h2>
                
                <!-- Search and Filters -->
                <div class="mb-6 space-y-4">
                    <div class="flex gap-4">
                        <div class="flex-1">
                            <flux:input 
                                wire:model.live.debounce.300ms="search" 
                                placeholder="{{ __('voting_centers.search_by_code_name') }}"
                                icon="magnifying-glass"
                            />
                        </div>
                        <flux:select wire:model.live="centerType" placeholder="{{ __('voting_centers.filter_by_type') }}">
                            <flux:option value="">{{ __('voting_centers.all') }}</flux:option>
                            <flux:option value="school">{{ __('voting_centers.school') }}</flux:option>
                            <flux:option value="community_center">{{ __('voting_centers.community_center') }}</flux:option>
                            <flux:option value="sports_facility">{{ __('voting_centers.sports_facility') }}</flux:option>
                            <flux:option value="government_building">{{ __('voting_centers.government_building') }}</flux:option>
                            <flux:option value="other">{{ __('voting_centers.other') }}</flux:option>
                        </flux:select>
                        <flux:select wire:model.live="status" placeholder="{{ __('voting_centers.filter_by_status') }}">
                            <flux:option value="">{{ __('voting_centers.all') }}</flux:option>
                            <flux:option value="active">{{ __('voting_centers.active') }}</flux:option>
                            <flux:option value="inactive">{{ __('voting_centers.inactive') }}</flux:option>
                            <flux:option value="maintenance">{{ __('voting_centers.maintenance') }}</flux:option>
                        </flux:select>
                        <flux:button wire:click="clearFilters" variant="outline">
                            {{ __('voting_centers.clear_filters') }}
                        </flux:button>
                        <flux:button :href="route('admin.voting-centers.create')" variant="primary">
                            {{ __('voting_centers.add_center') }}
                        </flux:button>
                    </div>
                </div>

                <!-- Results -->
                @if($votingCenters->count() > 0)
                    <div class="overflow-x-auto">
                        <flux:table :paginate="$votingCenters">
                            <flux:table.columns>
                                <flux:table.column sortable :sorted="$sortBy === 'code'" :direction="$sortDirection" wire:click="sort('code')">{{ __('voting_centers.center_code') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('voting_centers.center_name') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">{{ __('voting_centers.center_type') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'capacity'" :direction="$sortDirection" wire:click="sort('capacity')">{{ __('voting_centers.capacity') }}</flux:table.column>
                                <flux:table.column>{{ __('voting_centers.location') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">{{ __('voting_centers.status') }}</flux:table.column>
                                <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @foreach($votingCenters as $center)
                                    <flux:table.row :key="$center->id">
                                        <flux:table.cell class="font-medium">
                                            {{ $center->code }}
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <div>
                                                <div class="font-medium">{{ $center->name }}</div>
                                                @if($center->address)
                                                    <div class="text-sm text-zinc-500">{{ Str::limit($center->address, 50) }}</div>
                                                @endif
                                            </div>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" :color="match($center->type) {
                                                'school' => 'blue',
                                                'community_center' => 'green',
                                                'sports_facility' => 'yellow',
                                                'government_building' => 'purple',
                                                default => 'zinc'
                                            }">
                                                {{ __(match($center->type) {
                                                    'school' => 'voting_centers.school',
                                                    'community_center' => 'voting_centers.community_center',
                                                    'sports_facility' => 'voting_centers.sports_facility',
                                                    'government_building' => 'voting_centers.government_building',
                                                    default => 'voting_centers.other'
                                                }) }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell class="text-center">
                                            <div class="font-medium">{{ number_format($center->capacity) }}</div>
                                            @if($center->voting_tables_count)
                                                <div class="text-sm text-zinc-500">{{ $center->voting_tables_count }} {{ __('voting_centers.voting_tables') }}</div>
                                            @endif
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            @if($center->state)
                                                <div class="text-sm">
                                                    {{ $center->state->name }}
                                                    @if($center->municipality)
                                                        <br><span class="text-zinc-500">{{ $center->municipality->name }}</span>
                                                    @endif
                                                </div>
                                            @else
                                                <span class="text-zinc-400 text-sm">{{ __('voting_centers.no_location') }}</span>
                                            @endif
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" :color="match($center->status) {
                                                'active' => 'green',
                                                'inactive' => 'yellow',
                                                'maintenance' => 'orange',
                                                'closed' => 'red',
                                                default => 'zinc'
                                            }" inset="top bottom">
                                                {{ __(match($center->status) {
                                                    'active' => 'voting_centers.active',
                                                    'inactive' => 'voting_centers.inactive',
                                                    'maintenance' => 'voting_centers.maintenance',
                                                    'closed' => 'voting_centers.closed',
                                                    default => 'voting_centers.unknown'
                                                }) }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex gap-2">
                                                <flux:button size="sm" :href="route('admin.voting-centers.show', $center)" variant="outline">
                                                    {{ __('voting_centers.view_details') }}
                                                </flux:button>
                                                @can('update voting_centers')
                                                    <flux:button size="sm" :href="route('admin.voting-centers.edit', $center)" variant="outline">
                                                        {{ __('voting_centers.edit') }}
                                                    </flux:button>
                                                @endcan
                                                <flux:button variant="ghost" size="sm" icon="ellipsis-horizontal" inset="top bottom">
                                                    <flux:dropdown>
                                                        <flux:menu>
                                                            <flux:menu.item icon="eye" :href="route('admin.voting-centers.show', $center)">
                                                                {{ __('voting_centers.view_details') }}
                                                            </flux:menu.item>
                                                            @can('update voting_centers')
                                                                <flux:menu.item icon="pencil" :href="route('admin.voting-centers.edit', $center)">
                                                                    {{ __('voting_centers.edit') }}
                                                                </flux:menu.item>
                                                                <flux:menu.item icon="table-cells" :href="route('admin.voting-centers.tables', $center)">
                                                                    {{ __('voting_centers.manage_tables') }}
                                                                </flux:menu.item>
                                                            @endcan
                                                            @can('export voting_centers')
                                                                <flux:menu.separator />
                                                                <flux:menu.item icon="arrow-down-tray" wire:click="exportCenter({{ $center->id }})">
                                                                    {{ __('voting_centers.export') }}
                                                                </flux:menu.item>
                                                            @endcan
                                                            @can('delete voting_centers')
                                                                <flux:menu.separator />
                                                                <flux:menu.item icon="trash" variant="danger" wire:click="deleteCenter({{ $center->id }})" wire:confirm="{{ __('voting_centers.confirm_delete_center') }}">
                                                                    {{ __('voting_centers.delete') }}
                                                                </flux:menu.item>
                                                            @endcan
                                                        </flux:menu>
                                                    </flux:dropdown>
                                                </flux:button>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforeach
                            </flux:table.rows>
                        </flux:table>
                    </div>

                    <div class="mt-6">
                        {{ $votingCenters->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <flux:icon.building-office class="mx-auto h-12 w-12 text-zinc-400" />
                        <h3 class="mt-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ __('voting_centers.no_results') }}</h3>
                        <p class="mt-1 text-sm text-zinc-500">{{ __('voting_centers.no_results_description') }}</p>
                        <div class="mt-6">
                            <flux:button :href="route('admin.voting-centers.create')" variant="primary">
                                {{ __('voting_centers.add_center') }}
                            </flux:button>
                        </div>
                    </div>
                @endif
            </div>
        </flux:card>
    </div>
</div>
