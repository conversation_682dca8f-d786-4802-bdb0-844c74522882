<?php

return [
    '10_per_page' => '10 per page',
    '25_per_page' => '25 per page',
    '50_per_page' => '50 per page',
    '100_per_page' => '100 per page',
    'search_here' => 'Search here...',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'actions' => 'Actions',
    'id' => 'ID',
    'cancel' => 'Cancel',
    'this_action_is_irreversible' => 'This action is irreversible',
    'log_out' => 'Log out',
    'settings' => 'Settings',
    'go_to_frontend' => 'Go to frontend',
    'save' => 'Save',
    'saved' => 'Saved',
    'admin_dashboard' => 'Admin dashboard',
    'dashboard' => 'Dashboard',
    'back' => 'Back',
    'log_in' => 'Log in',
    'register' => 'Register',
    'log_into_your_account' => 'Log into your account',
    'log_into_your_account_text' => 'Enter your username and password below to log in',
    'email_address' => 'Email address',
    'username' => 'Username',
    'username_address' => 'Username',
    'username_placeholder' => 'my_username',
    'password' => 'Password',
    'forgot_password' => 'Forgot password',
    'remember_me' => 'Remember me',
    'dont_have_an_account' => 'Don\'t have an account?',
    'sign_up' => 'Sign up',
    'forgot_password_description' => 'Enter your email address below to reset your password',
    'send_password_reset_link' => 'Send password reset link',
    'or_return_to' => 'Or return to',
    'login' => 'Login',
    'create_an_account' => 'Create an account',
    'create_an_account_description' => 'Enter your details below to create an account',
    'confirm_password' => 'Confirm password',
    'already_have_an_account' => 'Already have an account?',
    'sign_in' => 'Sign in',
    'please_confirm_your_password_before_continuing' => 'Please confirm your password before continuing.',
    'confirm' => 'Confirm',
    'reset_password' => 'Reset password',
    'reset_password_description' => 'Enter your email address and new password below',
    'please_verify_your_email_address' => 'Please verify your email address by clicking on the link we just emailed to you.',
    'verification_link_sent' => 'A new verification link has been sent to the email address you provided during registration.',
    'resend_verification_email' => 'Resend verification email',
];
