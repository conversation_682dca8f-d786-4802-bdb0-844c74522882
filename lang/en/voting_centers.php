<?php

return [
    'title' => 'Voting Centers',
    'title_description' => 'Complete management of voting centers and electoral tables',

    // Navigation and menus
    'listing' => 'Listing',
    'add_center' => 'Add Center',
    'search_centers' => 'Search Centers',
    'center_details' => 'Center Details',
    'edit_center' => 'Edit Center',
    'manage_tables' => 'Manage Tables',

    // Basic information
    'center_code' => 'Center Code',
    'center_name' => 'Center Name',
    'center_type' => 'Center Type',
    'address' => 'Address',
    'capacity' => 'Capacity',
    'total_tables' => 'Total Tables',
    'active_tables' => 'Active Tables',

    // Center types
    'school' => 'School',
    'community_center' => 'Community Center',
    'sports_facility' => 'Sports Facility',
    'government_building' => 'Government Building',
    'other' => 'Other',

    // Geographic location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Contact information
    'contact_info' => 'Contact Information',
    'phone' => 'Phone',
    'email' => 'Email',
    'contact_person' => 'Contact Person',
    'contact_position' => 'Contact Position',

    // Center features
    'features' => 'Features',
    'has_parking' => 'Has Parking',
    'has_accessibility' => 'Wheelchair Accessible',
    'has_security' => 'Has Security',
    'has_backup_power' => 'Has Backup Power',
    'has_internet' => 'Has Internet',
    'has_air_conditioning' => 'Has Air Conditioning',

    // Center status
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'maintenance' => 'Under Maintenance',
    'closed' => 'Closed',

    // Voting tables
    'voting_tables' => 'Voting Tables',
    'table_number' => 'Table Number',
    'table_capacity' => 'Table Capacity',
    'registered_voters' => 'Registered Voters',
    'add_table' => 'Add Table',
    'edit_table' => 'Edit Table',
    'delete_table' => 'Delete Table',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'download_report' => 'Download Report',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_code_name' => 'Search by code or name',
    'filter_by_location' => 'Filter by location',
    'filter_by_type' => 'Filter by type',
    'filter_by_status' => 'Filter by status',
    'filter_by_capacity' => 'Filter by capacity',
    'advanced_search' => 'Advanced Search',

    // Statistics
    'statistics' => 'Statistics',
    'total_centers' => 'Total Centers',
    'active_centers' => 'Active Centers',
    'inactive_centers' => 'Inactive Centers',
    'total_capacity' => 'Total Capacity',
    'average_capacity' => 'Average Capacity',
    'centers_by_state' => 'Centers by State',
    'centers_by_type' => 'Centers by Type',

    // Messages
    'center_created' => 'Voting center created successfully',
    'center_updated' => 'Voting center updated successfully',
    'center_deleted' => 'Voting center deleted successfully',
    'table_created' => 'Voting table created successfully',
    'table_updated' => 'Voting table updated successfully',
    'table_deleted' => 'Voting table deleted successfully',
    'error_creating_center' => 'Error creating voting center',
    'error_updating_center' => 'Error updating voting center',
    'error_deleting_center' => 'Error deleting voting center',
    'center_code_exists' => 'A center with this code already exists',
    'no_results' => 'No centers found with applied filters',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'confirm_delete_center' => 'Are you sure you want to delete this voting center?',
    'confirm_delete_table' => 'Are you sure you want to delete this voting table?',
    'action_irreversible' => 'This action cannot be undone.',
    'center_has_tables' => 'This center has associated voting tables.',

    // Validations
    'center_code_required' => 'Center code is required',
    'center_name_required' => 'Center name is required',
    'center_type_required' => 'Center type is required',
    'address_required' => 'Address is required',
    'state_required' => 'State selection is required',
    'municipality_required' => 'Municipality selection is required',
    'parish_required' => 'Parish selection is required',
    'capacity_required' => 'Capacity is required',
    'capacity_numeric' => 'Capacity must be a number',
    'capacity_positive' => 'Capacity must be greater than zero',
    'table_number_required' => 'Table number is required',
    'table_number_unique' => 'A table with this number already exists in the center',

    // Export and import
    'export_excel' => 'Export to Excel',
    'export_pdf' => 'Export to PDF',
    'export_csv' => 'Export to CSV',
    'import_centers' => 'Import Centers',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    'file_formats' => 'Allowed formats: Excel (.xlsx), CSV (.csv)',
    'export_successful' => 'Export completed successfully',
    'import_successful' => 'Import completed successfully',
    'import_errors' => 'Errors found during import',

    // Additional information
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated',
    'created_by' => 'Created by',
    'updated_by' => 'Updated by',
    'coordinates' => 'Coordinates',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'map_location' => 'Map Location',

    // Reports
    'reports' => 'Reports',
    'center_report' => 'Center Report',
    'tables_report' => 'Tables Report',
    'capacity_report' => 'Capacity Report',
    'location_report' => 'Location Report',
    'generate_report' => 'Generate Report',

    // Permissions
    'view_voting_centers' => 'View Voting Centers',
    'create_voting_centers' => 'Create Voting Centers',
    'update_voting_centers' => 'Update Voting Centers',
    'delete_voting_centers' => 'Delete Voting Centers',
    'export_voting_centers' => 'Export Voting Centers',
    'import_voting_centers' => 'Import Voting Centers',
    'search_voting_centers' => 'Search Voting Centers',
    'view_voting_center_stats' => 'View Voting Center Statistics',
];
